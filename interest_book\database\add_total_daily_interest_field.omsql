-- Add totalDailyInterest field and implement daily interest accumulation system
-- This script adds totalDailyInterest field and creates events to accumulate daily interest
-- Created: 2025-07-04

-- Step 1: Add totalDailyInterest field to loan table
ALTER TABLE `loan` 
ADD COLUMN IF NOT EXISTS `totalDailyInterest` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Total accumulated daily interest over time';

-- Step 2: Initialize totalDailyInterest for existing loans
-- Calculate total daily interest based on days passed since loan start
UPDATE loan 
SET totalDailyInterest = CASE 
    WHEN startDate IS NOT NULL AND dailyInterest > 0 THEN
        ROUND(dailyInterest * DATEDIFF(CURDATE(), DATE(startDate)), 2)
    ELSE 0
END
WHERE updatedAmount > 0;

-- Step 3: Update existing triggers to also calculate totalDailyInterest

-- Drop existing triggers
DROP TRIGGER IF EXISTS `calculate_interest_on_loan_insert`;
DROP TRIGGER IF EXISTS `calculate_interest_on_deposit_insert`;
DROP TRIGGER IF EXISTS `calculate_interest_on_deposit_update`;
DROP TRIGGER IF EXISTS `calculate_interest_on_deposit_delete`;

-- Recreate loan insert trigger with totalDailyInterest calculation
DELIMITER $$

CREATE TRIGGER `calculate_interest_on_loan_insert`
BEFORE INSERT ON `loan`
FOR EACH ROW
BEGIN
    DECLARE total_deposits DECIMAL(10,2);
    DECLARE new_updated_amount DECIMAL(10,2);
    DECLARE new_monthly_interest DECIMAL(10,2);
    DECLARE new_daily_interest DECIMAL(10,2);
    DECLARE days_passed INT;
    DECLARE new_total_daily_interest DECIMAL(10,2);

    -- Calculate total deposits for this loan (should be 0 for new loans)
    SET total_deposits = 0;

    -- Calculate new updated amount (remaining balance)
    SET new_updated_amount = GREATEST(0, NEW.amount - total_deposits);

    -- Calculate new monthly interest on remaining balance
    SET new_monthly_interest = ROUND((new_updated_amount * NEW.rate) / 100, 2);

    -- Calculate new daily interest
    SET new_daily_interest = ROUND(new_monthly_interest / 30, 2);

    -- Calculate days passed since loan start
    SET days_passed = DATEDIFF(CURDATE(), DATE(NEW.startDate));

    -- Calculate total daily interest accumulated
    SET new_total_daily_interest = ROUND(new_daily_interest * GREATEST(0, days_passed), 2);

    -- Set calculated values directly in the NEW row
    SET NEW.totalDeposite = total_deposits;
    SET NEW.updatedAmount = new_updated_amount;
    SET NEW.interest = new_monthly_interest;
    SET NEW.dailyInterest = new_daily_interest;
    SET NEW.totalDailyInterest = new_total_daily_interest;
END$$

DELIMITER ;

-- Recreate deposit insert trigger with totalDailyInterest calculation
DELIMITER $$

CREATE TRIGGER `calculate_interest_on_deposit_insert`
AFTER INSERT ON `deposite`
FOR EACH ROW
BEGIN
    DECLARE loan_amount DECIMAL(10,2);
    DECLARE loan_rate DECIMAL(5,2);
    DECLARE total_deposits DECIMAL(10,2);
    DECLARE new_updated_amount DECIMAL(10,2);
    DECLARE new_monthly_interest DECIMAL(10,2);
    DECLARE new_daily_interest DECIMAL(10,2);
    DECLARE days_passed INT;
    DECLARE loan_start_date DATETIME;
    DECLARE new_total_daily_interest DECIMAL(10,2);
    
    -- Get loan details
    SELECT amount, rate, startDate INTO loan_amount, loan_rate, loan_start_date
    FROM loan 
    WHERE loanId = NEW.loanid;
    
    -- Calculate total deposits for this loan
    SELECT COALESCE(SUM(depositeAmount), 0) INTO total_deposits
    FROM deposite 
    WHERE loanid = NEW.loanid;
    
    -- Calculate new updated amount (remaining balance)
    SET new_updated_amount = GREATEST(0, loan_amount - total_deposits);
    
    -- Calculate new monthly interest on remaining balance
    SET new_monthly_interest = ROUND((new_updated_amount * loan_rate) / 100, 2);
    
    -- Calculate new daily interest
    SET new_daily_interest = ROUND(new_monthly_interest / 30, 2);
    
    -- Calculate days passed since loan start
    SET days_passed = DATEDIFF(CURDATE(), DATE(loan_start_date));
    
    -- Calculate total daily interest accumulated
    SET new_total_daily_interest = ROUND(new_daily_interest * GREATEST(0, days_passed), 2);
    
    -- Update loan table with all calculated values
    UPDATE loan 
    SET 
        totalDeposite = total_deposits,
        updatedAmount = new_updated_amount,
        interest = new_monthly_interest,
        dailyInterest = new_daily_interest,
        totalDailyInterest = new_total_daily_interest
    WHERE loanId = NEW.loanid;
END$$

DELIMITER ;

-- Recreate deposit update trigger with totalDailyInterest calculation
DELIMITER $$

CREATE TRIGGER `calculate_interest_on_deposit_update`
AFTER UPDATE ON `deposite`
FOR EACH ROW
BEGIN
    DECLARE loan_amount DECIMAL(10,2);
    DECLARE loan_rate DECIMAL(5,2);
    DECLARE total_deposits DECIMAL(10,2);
    DECLARE new_updated_amount DECIMAL(10,2);
    DECLARE new_monthly_interest DECIMAL(10,2);
    DECLARE new_daily_interest DECIMAL(10,2);
    DECLARE days_passed INT;
    DECLARE loan_start_date DATETIME;
    DECLARE new_total_daily_interest DECIMAL(10,2);
    
    -- Get loan details
    SELECT amount, rate, startDate INTO loan_amount, loan_rate, loan_start_date
    FROM loan 
    WHERE loanId = NEW.loanid;
    
    -- Calculate total deposits for this loan
    SELECT COALESCE(SUM(depositeAmount), 0) INTO total_deposits
    FROM deposite 
    WHERE loanid = NEW.loanid;
    
    -- Calculate new updated amount (remaining balance)
    SET new_updated_amount = GREATEST(0, loan_amount - total_deposits);
    
    -- Calculate new monthly interest on remaining balance
    SET new_monthly_interest = ROUND((new_updated_amount * loan_rate) / 100, 2);
    
    -- Calculate new daily interest
    SET new_daily_interest = ROUND(new_monthly_interest / 30, 2);
    
    -- Calculate days passed since loan start
    SET days_passed = DATEDIFF(CURDATE(), DATE(loan_start_date));
    
    -- Calculate total daily interest accumulated
    SET new_total_daily_interest = ROUND(new_daily_interest * GREATEST(0, days_passed), 2);
    
    -- Update loan table with all calculated values
    UPDATE loan 
    SET 
        totalDeposite = total_deposits,
        updatedAmount = new_updated_amount,
        interest = new_monthly_interest,
        dailyInterest = new_daily_interest,
        totalDailyInterest = new_total_daily_interest
    WHERE loanId = OLD.loanid;
END$$

DELIMITER ;

-- Recreate deposit delete trigger with totalDailyInterest calculation
DELIMITER $$

CREATE TRIGGER `calculate_interest_on_deposit_delete`
AFTER DELETE ON `deposite`
FOR EACH ROW
BEGIN
    DECLARE loan_amount DECIMAL(10,2);
    DECLARE loan_rate DECIMAL(5,2);
    DECLARE total_deposits DECIMAL(10,2);
    DECLARE new_updated_amount DECIMAL(10,2);
    DECLARE new_monthly_interest DECIMAL(10,2);
    DECLARE new_daily_interest DECIMAL(10,2);
    DECLARE days_passed INT;
    DECLARE loan_start_date DATETIME;
    DECLARE new_total_daily_interest DECIMAL(10,2);
    
    -- Get loan details
    SELECT amount, rate, startDate INTO loan_amount, loan_rate, loan_start_date
    FROM loan 
    WHERE loanId = OLD.loanid;
    
    -- Calculate total deposits for this loan
    SELECT COALESCE(SUM(depositeAmount), 0) INTO total_deposits
    FROM deposite 
    WHERE loanid = OLD.loanid;
    
    -- Calculate new updated amount (remaining balance)
    SET new_updated_amount = GREATEST(0, loan_amount - total_deposits);
    
    -- Calculate new monthly interest on remaining balance
    SET new_monthly_interest = ROUND((new_updated_amount * loan_rate) / 100, 2);
    
    -- Calculate new daily interest
    SET new_daily_interest = ROUND(new_monthly_interest / 30, 2);
    
    -- Calculate days passed since loan start
    SET days_passed = DATEDIFF(CURDATE(), DATE(loan_start_date));
    
    -- Calculate total daily interest accumulated
    SET new_total_daily_interest = ROUND(new_daily_interest * GREATEST(0, days_passed), 2);
    
    -- Update loan table with all calculated values
    UPDATE loan 
    SET 
        totalDeposite = total_deposits,
        updatedAmount = new_updated_amount,
        interest = new_monthly_interest,
        dailyInterest = new_daily_interest,
        totalDailyInterest = new_total_daily_interest
    WHERE loanId = OLD.loanid;
END$$

DELIMITER ;

-- Step 4: Create daily interest accumulation event
-- This event runs daily to add dailyInterest to totalDailyInterest for all active loans

DROP EVENT IF EXISTS `accumulate_daily_interest`;

DELIMITER $$

CREATE EVENT `accumulate_daily_interest`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
ON COMPLETION PRESERVE
ENABLE
DO
BEGIN
    -- Update totalDailyInterest for all active loans
    -- Only add daily interest if the loan is still active AND we're beyond monthly calculation period
    UPDATE loan
    SET totalDailyInterest = totalDailyInterest + dailyInterest
    WHERE (endDate IS NULL OR endDate > CURDATE())
    AND updatedAmount > 0
    AND dailyInterest > 0
    AND DATEDIFF(CURDATE(), DATE(startDate)) > (FLOOR(DATEDIFF(CURDATE(), DATE(startDate)) / 30) * 30);

    -- Log the daily interest accumulation (optional)
    INSERT INTO system_log (log_date, log_message, log_type)
    VALUES (NOW(), CONCAT('Daily interest accumulated for ', ROW_COUNT(), ' active loans beyond monthly period'), 'DAILY_INTEREST')
    ON DUPLICATE KEY UPDATE log_message = VALUES(log_message);
END$$

DELIMITER ;

-- Step 5: Create system_log table if it doesn't exist (for logging daily interest accumulation)
CREATE TABLE IF NOT EXISTS `system_log` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `log_date` datetime NOT NULL,
  `log_message` text NOT NULL,
  `log_type` varchar(50) NOT NULL,
  PRIMARY KEY (`log_id`),
  KEY `idx_log_date` (`log_date`),
  KEY `idx_log_type` (`log_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Step 6: Show current loan data with all interest calculations
SELECT
    loanId,
    amount,
    updatedAmount,
    rate,
    startDate,
    interest as monthly_interest,
    dailyInterest as daily_interest,
    totalDailyInterest as total_daily_interest,
    totalInterest,
    lastInterestUpdatedAt,
    DATEDIFF(CURDATE(), DATE(startDate)) as days_since_start,
    ROUND((updatedAmount * rate) / 100, 2) as calculated_monthly,
    ROUND((updatedAmount * rate) / 100 / 30, 2) as calculated_daily
FROM loan
WHERE updatedAmount > 0
ORDER BY startDate;

-- Step 7: Show all events related to interest calculation
SHOW EVENTS WHERE Name LIKE '%interest%';

-- Step 8: Show all triggers related to loan table
SHOW TRIGGERS WHERE `Table` = 'loan';

-- Step 9: Verify the new column was added
DESCRIBE loan;
