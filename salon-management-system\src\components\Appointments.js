import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

const Appointments = () => {
  const [appointments, setAppointments] = useState([
    {
      id: 1,
      customer: '<PERSON>',
      phone: '(*************',
      service: 'Hair Cut & Style',
      stylist: '<PERSON>',
      date: '2024-01-15',
      time: '9:00 AM',
      duration: '60 min',
      price: '$85',
      status: 'completed',
    },
    {
      id: 2,
      customer: '<PERSON>',
      phone: '(*************',
      service: 'Beard Trim',
      stylist: '<PERSON>',
      date: '2024-01-15',
      time: '10:30 AM',
      duration: '30 min',
      price: '$35',
      status: 'in-progress',
    },
    {
      id: 3,
      customer: '<PERSON>',
      phone: '(*************',
      service: 'Hair Color',
      stylist: '<PERSON>',
      date: '2024-01-15',
      time: '11:00 AM',
      duration: '120 min',
      price: '$150',
      status: 'scheduled',
    },
    {
      id: 4,
      customer: 'Tom Wilson',
      phone: '(*************',
      service: 'Full Service',
      stylist: '<PERSON> <PERSON>',
      date: '2024-01-15',
      time: '2:00 PM',
      duration: '90 min',
      price: '$120',
      status: 'scheduled',
    },
  ]);

  const [open, setOpen] = useState(false);
  const [editingAppointment, setEditingAppointment] = useState(null);
  const [formData, setFormData] = useState({
    customer: '',
    phone: '',
    service: '',
    stylist: '',
    date: '',
    time: '',
    duration: '',
    price: '',
    status: 'scheduled',
  });

  const services = [
    'Hair Cut & Style',
    'Hair Color',
    'Beard Trim',
    'Full Service',
    'Manicure',
    'Pedicure',
    'Facial',
    'Massage',
  ];

  const stylists = [
    'Emma Wilson',
    'John Smith',
    'Mike Johnson',
    'Sarah Davis',
    'Lisa Anderson',
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'scheduled':
        return 'primary';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const handleOpen = (appointment = null) => {
    if (appointment) {
      setEditingAppointment(appointment);
      setFormData(appointment);
    } else {
      setEditingAppointment(null);
      setFormData({
        customer: '',
        phone: '',
        service: '',
        stylist: '',
        date: '',
        time: '',
        duration: '',
        price: '',
        status: 'scheduled',
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingAppointment(null);
  };

  const handleSave = () => {
    if (editingAppointment) {
      setAppointments(appointments.map(apt => 
        apt.id === editingAppointment.id ? { ...formData, id: editingAppointment.id } : apt
      ));
    } else {
      const newAppointment = {
        ...formData,
        id: Math.max(...appointments.map(a => a.id)) + 1,
      };
      setAppointments([...appointments, newAppointment]);
    }
    handleClose();
  };

  const handleDelete = (id) => {
    setAppointments(appointments.filter(apt => apt.id !== id));
  };

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const todayStats = {
    total: appointments.filter(apt => apt.date === '2024-01-15').length,
    completed: appointments.filter(apt => apt.date === '2024-01-15' && apt.status === 'completed').length,
    scheduled: appointments.filter(apt => apt.date === '2024-01-15' && apt.status === 'scheduled').length,
    inProgress: appointments.filter(apt => apt.date === '2024-01-15' && apt.status === 'in-progress').length,
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Appointments</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          New Appointment
        </Button>
      </Box>

      {/* Today's Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Today
              </Typography>
              <Typography variant="h4">
                {todayStats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h4" color="success.main">
                {todayStats.completed}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                In Progress
              </Typography>
              <Typography variant="h4" color="warning.main">
                {todayStats.inProgress}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Scheduled
              </Typography>
              <Typography variant="h4" color="primary.main">
                {todayStats.scheduled}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Appointments Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Customer</TableCell>
              <TableCell>Phone</TableCell>
              <TableCell>Service</TableCell>
              <TableCell>Stylist</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Time</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Price</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {appointments.map((appointment) => (
              <TableRow key={appointment.id}>
                <TableCell>{appointment.customer}</TableCell>
                <TableCell>{appointment.phone}</TableCell>
                <TableCell>{appointment.service}</TableCell>
                <TableCell>{appointment.stylist}</TableCell>
                <TableCell>{appointment.date}</TableCell>
                <TableCell>{appointment.time}</TableCell>
                <TableCell>{appointment.duration}</TableCell>
                <TableCell>{appointment.price}</TableCell>
                <TableCell>
                  <Chip
                    label={appointment.status}
                    color={getStatusColor(appointment.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => handleOpen(appointment)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDelete(appointment.id)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add/Edit Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingAppointment ? 'Edit Appointment' : 'New Appointment'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Customer Name"
                value={formData.customer}
                onChange={(e) => handleInputChange('customer', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Service"
                value={formData.service}
                onChange={(e) => handleInputChange('service', e.target.value)}
              >
                {services.map((service) => (
                  <MenuItem key={service} value={service}>
                    {service}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Stylist"
                value={formData.stylist}
                onChange={(e) => handleInputChange('stylist', e.target.value)}
              >
                {stylists.map((stylist) => (
                  <MenuItem key={stylist} value={stylist}>
                    {stylist}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="date"
                label="Date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="time"
                label="Time"
                value={formData.time}
                onChange={(e) => handleInputChange('time', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Duration"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', e.target.value)}
                placeholder="e.g., 60 min"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Price"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="e.g., $85"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                select
                label="Status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="in-progress">In Progress</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </TextField>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">
            {editingAppointment ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Appointments;
