-- Fix circular dependency issue in loan triggers
-- This script replaces the problematic triggers with corrected versions
-- Created: 2025-07-04

-- Step 1: Drop all existing problematic triggers and procedures
DROP TRIGGER IF EXISTS `calculate_interest_on_loan_insert`;
DROP TRIGGER IF EXISTS `calculate_interest_on_deposit_insert`;
DROP TRIGGER IF EXISTS `calculate_interest_on_deposit_update`;
DROP TRIGGER IF EXISTS `calculate_interest_on_deposit_delete`;
DROP PROCEDURE IF EXISTS `UpdateLoanCalculations`;

-- Step 2: Create corrected loan insert trigger (BEFORE INSERT)
DELIMITER $$

CREATE TRIGGER `calculate_interest_on_loan_insert`
BEFORE INSERT ON `loan`
FOR EACH ROW
BEGIN
    -- All DECLARE statements must be at the beginning
    DECLARE new_monthly_interest DECIMAL(10,2);
    DECLARE new_daily_interest DECIMAL(10,2);
    DECLARE new_total_daily_interest DECIMAL(10,2);
    DECLARE total_days_passed INT;
    DECLARE complete_months INT;
    DECLARE days_beyond_monthly INT;

    -- Calculate new monthly interest on loan amount (no deposits yet for new loans)
    SET new_monthly_interest = ROUND((NEW.amount * NEW.rate) / 100, 2);

    -- Calculate new daily interest
    SET new_daily_interest = ROUND(new_monthly_interest / 30, 2);

    -- Calculate days beyond monthly calculation period
    -- Daily interest only starts accumulating after complete months
    SET total_days_passed = DATEDIFF(CURDATE(), DATE(NEW.startDate));
    SET complete_months = FLOOR(total_days_passed / 30);
    SET days_beyond_monthly = total_days_passed - (complete_months * 30);

    -- Total daily interest only for days beyond monthly calculation
    SET new_total_daily_interest = ROUND(new_daily_interest * GREATEST(0, days_beyond_monthly), 2);
    
    -- Set calculated values directly in the NEW row
    SET NEW.updatedAmount = NEW.amount; -- For new loans, updated amount equals original amount
    SET NEW.totalDeposite = 0; -- No deposits yet for new loans
    SET NEW.interest = new_monthly_interest;
    SET NEW.dailyInterest = new_daily_interest;
    SET NEW.totalDailyInterest = new_total_daily_interest;
END$$

DELIMITER ;

-- Step 3: Create stored procedure for updating loan calculations
DELIMITER $$

CREATE PROCEDURE `UpdateLoanCalculations`(IN loan_id INT)
BEGIN
    -- All DECLARE statements must be at the beginning
    DECLARE loan_amount DECIMAL(10,2);
    DECLARE loan_rate DECIMAL(5,2);
    DECLARE loan_start_date DATETIME;
    DECLARE total_deposits DECIMAL(10,2);
    DECLARE new_updated_amount DECIMAL(10,2);
    DECLARE new_monthly_interest DECIMAL(10,2);
    DECLARE new_daily_interest DECIMAL(10,2);
    DECLARE new_total_daily_interest DECIMAL(10,2);
    DECLARE total_days_passed INT;
    DECLARE complete_months INT;
    DECLARE days_beyond_monthly INT;

    -- Get loan details
    SELECT amount, rate, startDate INTO loan_amount, loan_rate, loan_start_date
    FROM loan
    WHERE loanId = loan_id;

    -- Calculate total deposits for this loan
    SELECT COALESCE(SUM(depositeAmount), 0) INTO total_deposits
    FROM deposite
    WHERE loanid = loan_id;

    -- Calculate new updated amount (remaining balance)
    SET new_updated_amount = GREATEST(0, loan_amount - total_deposits);

    -- Calculate new monthly interest on remaining balance
    SET new_monthly_interest = ROUND((new_updated_amount * loan_rate) / 100, 2);

    -- Calculate new daily interest
    SET new_daily_interest = ROUND(new_monthly_interest / 30, 2);

    -- Calculate days beyond monthly calculation period
    -- Daily interest only starts accumulating after complete months
    SET total_days_passed = DATEDIFF(CURDATE(), DATE(loan_start_date));
    SET complete_months = FLOOR(total_days_passed / 30);
    SET days_beyond_monthly = total_days_passed - (complete_months * 30);

    -- Total daily interest only for days beyond monthly calculation
    SET new_total_daily_interest = ROUND(new_daily_interest * GREATEST(0, days_beyond_monthly), 2);
    
    -- Update loan table with all calculated values
    UPDATE loan 
    SET 
        totalDeposite = total_deposits,
        updatedAmount = new_updated_amount,
        interest = new_monthly_interest,
        dailyInterest = new_daily_interest,
        totalDailyInterest = new_total_daily_interest
    WHERE loanId = loan_id;
END$$

DELIMITER ;

-- Step 4: Create deposit triggers that call the stored procedure
DELIMITER $$

CREATE TRIGGER `calculate_interest_on_deposit_insert`
AFTER INSERT ON `deposite`
FOR EACH ROW
BEGIN
    CALL UpdateLoanCalculations(NEW.loanid);
END$$

DELIMITER ;

DELIMITER $$

CREATE TRIGGER `calculate_interest_on_deposit_update`
AFTER UPDATE ON `deposite`
FOR EACH ROW
BEGIN
    CALL UpdateLoanCalculations(NEW.loanid);
END$$

DELIMITER ;

DELIMITER $$

CREATE TRIGGER `calculate_interest_on_deposit_delete`
AFTER DELETE ON `deposite`
FOR EACH ROW
BEGIN
    CALL UpdateLoanCalculations(OLD.loanid);
END$$

DELIMITER ;

-- Step 5: Update existing loans to have correct calculations
UPDATE loan
SET
    interest = ROUND((updatedAmount * rate) / 100, 2),
    dailyInterest = ROUND((updatedAmount * rate) / 100 / 30, 2),
    totalDailyInterest = ROUND(
        (ROUND((updatedAmount * rate) / 100 / 30, 2)) *
        GREATEST(0, DATEDIFF(CURDATE(), DATE(startDate)) - (FLOOR(DATEDIFF(CURDATE(), DATE(startDate)) / 30) * 30)), 2
    )
WHERE updatedAmount > 0;

-- Step 6: Show updated triggers
SHOW TRIGGERS WHERE `Table` = 'loan';
SHOW TRIGGERS WHERE `Table` = 'deposite';

-- Step 7: Show stored procedures
SHOW PROCEDURE STATUS WHERE Name = 'UpdateLoanCalculations';

-- Step 8: Test the procedure with a sample loan (replace 1 with actual loan ID)
-- CALL UpdateLoanCalculations(1);
