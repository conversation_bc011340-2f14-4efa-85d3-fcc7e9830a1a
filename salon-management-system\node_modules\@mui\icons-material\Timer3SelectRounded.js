"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)(/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M21 12c0 .55-.45 1-1 1h-3v1h2.5c.83 0 1.5.68 1.5 1.5v2c0 .83-.67 1.5-1.5 1.5H16c-.55 0-1-.45-1-1s.45-1 1-1h3v-1h-2.5c-.82 0-1.5-.68-1.5-1.5v-2c0-.82.68-1.5 1.5-1.5H20c.55 0 1 .45 1 1M4 6.5C4 7.33 4.67 8 5.5 8H10v2.5H5.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5H10V16H5.5c-.83 0-1.5.67-1.5 1.5S4.67 19 5.5 19H10c1.66 0 3-1.34 3-3v-1.9c0-1.16-.94-2.1-2.1-2.1 1.16 0 2.1-.94 2.1-2.1V8c0-1.66-1.34-3-3-3H5.5C4.67 5 4 5.67 4 6.5"
}), 'Timer3SelectRounded');